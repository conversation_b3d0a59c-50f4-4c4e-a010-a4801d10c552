// Package database
// @Link  https://github.com/bufanyun/hotgo
// @Copyright  Copyright (c) 2023 HotGo CLI
// <AUTHOR> <<EMAIL>>
// @License  https://github.com/bufanyun/hotgo/blob/master/LICENSE
package database

import (
	"context"
	"fmt"
	"strings"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
)

// InitDatabase 初始化数据库配置
func InitDatabase(ctx context.Context) error {
	dbType := g.Cfg().MustGet(ctx, "database.type").String()
	if dbType == "" {
		dbType = "mysql" // 默认使用 MySQL
	}

	g.Log().Infof(ctx, "初始化数据库，类型: %s", dbType)

	switch dbType {
	case "mysql":
		return initMySQL(ctx)
	case "sqlite":
		return initSQLite(ctx)
	default:
		return fmt.Errorf("不支持的数据库类型: %s", dbType)
	}
}

// initMySQL 初始化 MySQL 数据库
func initMySQL(ctx context.Context) error {
	// MySQL 使用默认配置，无需特殊初始化
	g.Log().Info(ctx, "使用 MySQL 数据库")
	return nil
}

// initSQLite 初始化 SQLite 数据库
func initSQLite(ctx context.Context) error {
	g.Log().Info(ctx, "使用 SQLite 数据库")

	// 获取 SQLite 数据库文件路径
	sqliteLink := g.Cfg().MustGet(ctx, "database.sqlite.link").String()
	if sqliteLink == "" {
		return fmt.Errorf("SQLite 数据库连接配置不能为空")
	}

	// 从连接字符串中提取数据库文件路径
	// 格式: sqlite::@file(storage/data/goview.db)
	dbPath := "storage/data/goview.db"

	// 确保数据库目录存在
	dbDir := gfile.Dir(dbPath)
	if !gfile.Exists(dbDir) {
		if err := gfile.Mkdir(dbDir); err != nil {
			return fmt.Errorf("创建数据库目录失败: %v", err)
		}
	}

	// 重新配置数据库连接，使用 SQLite 配置
	err := switchToSQLiteConfig(ctx)
	if err != nil {
		return fmt.Errorf("切换到 SQLite 配置失败: %v", err)
	}

	// 检查数据库文件是否存在
	if !gfile.Exists(dbPath) {
		g.Log().Infof(ctx, "SQLite 数据库文件不存在，将创建新的数据库文件: %s", dbPath)
	} else {
		g.Log().Infof(ctx, "SQLite 数据库文件已存在: %s", dbPath)
	}

	// 无论文件是否存在，都尝试创建表结构（使用 CREATE TABLE IF NOT EXISTS）
	err = createSQLiteTables(ctx)
	if err != nil {
		return fmt.Errorf("创建 SQLite 表结构失败: %v", err)
	}

	return nil
}

// switchToSQLiteConfig 切换到 SQLite 配置
func switchToSQLiteConfig(ctx context.Context) error {
	g.Log().Info(ctx, "切换到 SQLite 数据库配置")

	// 在 GoFrame 中，我们需要通过修改配置文件或环境变量来切换数据库
	// 这里我们采用一个简单的方法：直接使用 SQLite 配置创建数据库连接
	// 用户需要手动修改配置文件中的 database.type 为 "sqlite"
	// 并将 database.sqlite 的配置复制到 database.default

	g.Log().Info(ctx, "请确保配置文件中 database.type 设置为 'sqlite'")
	g.Log().Info(ctx, "并将 database.sqlite 配置复制到 database.default")

	return nil
}

// createSQLiteTables 创建 SQLite 表结构
func createSQLiteTables(ctx context.Context) error {
	g.Log().Info(ctx, "开始创建 SQLite 表结构")

	// 读取 SQLite 初始化脚本
	sqlFile := "storage/data/goview_sqlite.sql"
	if !gfile.Exists(sqlFile) {
		return fmt.Errorf("SQLite 初始化脚本不存在: %s", sqlFile)
	}

	sqlContent := gfile.GetContents(sqlFile)
	if sqlContent == "" {
		return fmt.Errorf("SQLite 初始化脚本内容为空")
	}

	db := g.DB()

	// 执行 SQL 脚本
	// 将脚本按分号分割成多个语句
	statements := splitSQLStatements(sqlContent)

	for i, statement := range statements {
		statement = strings.TrimSpace(statement)
		if statement == "" || strings.HasPrefix(statement, "--") {
			continue
		}

		g.Log().Debugf(ctx, "执行 SQL 语句 %d: %s", i+1, statement[:min(100, len(statement))])

		_, err := db.Exec(ctx, statement)
		if err != nil {
			return fmt.Errorf("执行 SQL 语句失败 (语句 %d): %v\nSQL: %s", i+1, err, statement)
		}
	}

	g.Log().Info(ctx, "SQLite 表结构创建完成")
	return nil
}

// splitSQLStatements 分割 SQL 语句
func splitSQLStatements(sqlContent string) []string {
	// 简单的 SQL 语句分割，按分号分割
	// 注意：这个实现比较简单，不处理字符串中的分号
	statements := strings.Split(sqlContent, ";")
	var result []string

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt != "" && !strings.HasPrefix(stmt, "--") {
			result = append(result, stmt)
		}
	}

	return result
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
